import React, { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Copy } from "lucide-react";
import { getBlockExplorers, getExplorerUrl } from "@/lib/chainConfig";
import { useToast } from "@/hooks/use-toast";

const BlockExplorerDemo = () => {
  const { toast } = useToast();
  const [selectedChain, setSelectedChain] = useState(1);
  const [currentExplorer, setCurrentExplorer] = useState<any>(null);
  const [explorerUrls, setExplorerUrls] = useState<Record<string, string>>({});

  // Sample data for demonstration
  const sampleAddress = "******************************************";
  const sampleTxHash =
    "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
  const sampleBlockNumber = "18500000";
  const sampleTokenAddress = "******************************************";

  // Popular chains for demo
  const demoChains = [
    { id: 1, name: "Ethereum Mainnet", color: "bg-blue-500" },
    { id: 137, name: "Polygon", color: "bg-purple-500" },
    { id: 56, name: "BSC", color: "bg-yellow-500" },
    { id: 42161, name: "Arbitrum", color: "bg-blue-400" },
    { id: 10, name: "Optimism", color: "bg-red-500" },
    { id: 8453, name: "Base", color: "bg-blue-600" },
    { id: 43114, name: "Avalanche", color: "bg-red-400" },
    { id: 250, name: "Fantom", color: "bg-blue-300" },
    { id: 11155111, name: "Sepolia Testnet", color: "bg-gray-500" },
    { id: 80002, name: "Polygon Amoy", color: "bg-purple-400" },
  ];

  // Load explorer data when chain changes
  useEffect(() => {
    const loadExplorerData = async () => {
      try {
        const explorers = await getBlockExplorers(selectedChain);
        setCurrentExplorer(explorers[0]);

        // Pre-generate URLs for all types
        const urls = {
          address: await getExplorerUrl(
            selectedChain,
            "address",
            sampleAddress
          ),
          tx: await getExplorerUrl(selectedChain, "tx", sampleTxHash),
          block: await getExplorerUrl(
            selectedChain,
            "block",
            sampleBlockNumber
          ),
          token: await getExplorerUrl(
            selectedChain,
            "token",
            sampleTokenAddress
          ),
        };
        setExplorerUrls(urls);
      } catch (error) {
        console.error("Failed to load explorer data:", error);
      }
    };

    loadExplorerData();
  }, [
    selectedChain,
    sampleAddress,
    sampleTxHash,
    sampleBlockNumber,
    sampleTokenAddress,
  ]);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: `${label} copied to clipboard`,
      duration: 2000,
    });
  };

  const openExplorer = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  return (
    <Card className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">
          Multi-Chain Block Explorer Demo
        </h2>
        <p className="text-muted-foreground">
          Select different blockchain networks to see how explorer links are
          generated for each chain.
        </p>
      </div>

      {/* Chain Selector */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">
          Select Blockchain Network
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
          {demoChains.map((chain) => (
            <Button
              key={chain.id}
              variant={selectedChain === chain.id ? "default" : "outline"}
              size="sm"
              className="justify-start"
              onClick={() => setSelectedChain(chain.id)}
            >
              <div className={`w-3 h-3 rounded-full ${chain.color} mr-2`} />
              {chain.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Current Explorer Info */}
      <div className="mb-6 p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-semibold">Current Explorer</h4>
            <p className="text-sm text-muted-foreground">
              Chain ID: {selectedChain} • Explorer: {currentExplorer?.name}
            </p>
          </div>
          <Badge variant="secondary">{currentExplorer?.name}</Badge>
        </div>
      </div>

      {/* Explorer Links Demo */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Explorer Links</h3>

        {/* Address Link */}
        <div className="p-4 border rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium">Address Explorer</h4>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() =>
                  copyToClipboard(explorerUrls.address || "", "Address URL")
                }
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                onClick={() => openExplorer(explorerUrls.address || "")}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                View
              </Button>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mb-2">
            Sample Address: {sampleAddress}
          </p>
          <code className="text-xs bg-background p-2 rounded block break-all">
            {explorerUrls.address || "Loading..."}
          </code>
        </div>

        {/* Transaction Link */}
        <div className="p-4 border rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium">Transaction Explorer</h4>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() =>
                  copyToClipboard(explorerUrls.tx || "", "Transaction URL")
                }
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                onClick={() => openExplorer(explorerUrls.tx || "")}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                View
              </Button>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mb-2">
            Sample Transaction Hash
          </p>
          <code className="text-xs bg-background p-2 rounded block break-all">
            {explorerUrls.tx || "Loading..."}
          </code>
        </div>

        {/* Block Link */}
        <div className="p-4 border rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium">Block Explorer</h4>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() =>
                  copyToClipboard(explorerUrls.block || "", "Block URL")
                }
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                onClick={() => openExplorer(explorerUrls.block || "")}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                View
              </Button>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mb-2">
            Sample Block Number: {sampleBlockNumber}
          </p>
          <code className="text-xs bg-background p-2 rounded block break-all">
            {explorerUrls.block || "Loading..."}
          </code>
        </div>

        {/* Token Link */}
        <div className="p-4 border rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium">Token Explorer</h4>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() =>
                  copyToClipboard(explorerUrls.token || "", "Token URL")
                }
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                onClick={() => openExplorer(explorerUrls.token || "")}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                View
              </Button>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mb-2">
            Sample Token Address: {sampleTokenAddress}
          </p>
          <code className="text-xs bg-background p-2 rounded block break-all">
            {explorerUrls.token || "Loading..."}
          </code>
        </div>
      </div>
    </Card>
  );
};

export default BlockExplorerDemo;
