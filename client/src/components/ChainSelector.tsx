import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  supportedChains,
  getChainById,
  getCachedChainMetadata,
  chainColors,
} from "@/lib/chainConfig";
import { SearchIcon, CheckIcon, ChevronLeftIcon } from "lucide-react";
import {
  useActiveWalletChain,
  useSwitchActiveWalletChain,
} from "thirdweb/react";
import type { Chain } from "thirdweb/chains";
import { useToast } from "@/hooks/use-toast";

interface ChainSelectorProps {
  onClose?: () => void;
}

const ChainSelector = ({ onClose }: ChainSelectorProps) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [chainMetadata, setChainMetadata] = useState<Record<number, any>>({});
  const [pendingChainId, setPendingChainId] = useState<number | null>(null);
  const [isSwitching, setIsSwitching] = useState(false);
  const { toast } = useToast();

  // Get current chain from wallet using v5 hooks
  const activeChain = useActiveWalletChain();
  const switchChain = useSwitchActiveWalletChain();

  // Get current chain ID or default to Ethereum
  const currentChainId = activeChain?.id || 1;
  const selectedChain = getChainById(currentChainId) || supportedChains[0];

  // Use pending chain for UI display if switching
  const displayChainId = pendingChainId || currentChainId;

  // Reset pending state when actual chain changes
  useEffect(() => {
    if (pendingChainId && currentChainId === pendingChainId) {
      setPendingChainId(null);
      setIsSwitching(false);
    }
  }, [currentChainId, pendingChainId]);

  // Debug logging
  console.log("ChainSelector v5 - activeChain:", activeChain);
  console.log("ChainSelector v5 - currentChainId:", currentChainId);
  console.log("ChainSelector v5 - selectedChain:", selectedChain);
  console.log("ChainSelector v5 - pendingChainId:", pendingChainId);
  console.log("ChainSelector v5 - displayChainId:", displayChainId);

  // Load chain metadata for filtering
  useEffect(() => {
    const loadMetadata = async () => {
      const metadata: Record<number, any> = {};
      for (const chain of supportedChains) {
        try {
          const meta = await getCachedChainMetadata(chain);
          if (meta) {
            metadata[chain.id] = meta;
          }
        } catch (error) {
          console.warn(`Failed to load metadata for chain ${chain.id}:`, error);
        }
      }
      setChainMetadata(metadata);
    };
    loadMetadata();
  }, []);

  // Filter chains based on search query
  const filteredChains = supportedChains.filter((chain) => {
    const meta = chainMetadata[chain.id];
    const chainName = meta?.name || `Chain ${chain.id}`;
    return (
      chainName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chain.id.toString().includes(searchQuery)
    );
  });

  // Handle chain selection using v5 switchChain with optimistic UI
  const handleSelectChain = async (chainId: number) => {
    const chainOption = getChainById(chainId);
    if (!chainOption) return;

    // If this is already the current chain, just close
    if (chainId === currentChainId) {
      if (onClose) onClose();
      return;
    }

    // Immediately update UI (optimistic update)
    setPendingChainId(chainId);
    setIsSwitching(true);

    // Close the dropdown immediately for better UX
    if (onClose) onClose();

    const meta = chainMetadata[chainId];
    console.log(
      "Attempting to switch to chain:",
      chainId,
      meta?.name || `Chain ${chainId}`
    );

    try {
      // Use v5 switchChain function with the chain object directly
      await switchChain(chainOption);

      console.log("Successfully switched to chain:", chainId);

      // Clear pending state on success
      setPendingChainId(null);
      setIsSwitching(false);
    } catch (error: any) {
      console.error("Failed to switch chain with v5:", error);

      // Revert optimistic update on error
      setPendingChainId(null);
      setIsSwitching(false);

      // Show error toast
      toast({
        title: "Chain Switch Failed",
        description:
          error.message ||
          "Failed to switch to the selected network. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Helper function to get chain display name
  const getChainDisplayName = (chain: Chain): string => {
    const meta = chainMetadata[chain.id];
    return meta?.name || `Chain ${chain.id}`;
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        if (onClose) onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  return (
    <div
      className="absolute bottom-full left-0 mb-2 w-64 bg-popover border border-border rounded-lg shadow-xl z-[100]"
      ref={dropdownRef}
    >
      {/* Header */}
      <div className="flex items-center gap-2 p-3 border-b border-border">
        <Button
          variant="ghost"
          size="sm"
          className="p-1 h-auto text-muted-foreground hover:text-foreground hover:bg-primary/10 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
          onClick={onClose}
        >
          <ChevronLeftIcon className="h-4 w-4" />
        </Button>
        <span className="text-popover-foreground font-medium text-sm">
          Select Network
        </span>
      </div>

      {/* Search */}
      <div className="p-3 border-b border-border">
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by Name or Chain Id"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-input border-border text-foreground placeholder-muted-foreground focus:border-primary h-8 text-sm focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        </div>
      </div>

      {/* Chain List */}
      <div className="max-h-48 overflow-y-auto scrollbar-hide">
        {filteredChains.map((chainOption) => {
          // Use displayChainId for optimistic UI updates
          const isSelected = chainOption.id === displayChainId;
          const isPending = chainOption.id === pendingChainId && isSwitching;
          const chainName = getChainDisplayName(chainOption);

          console.log(
            `Chain ${chainName}: chainId=${chainOption.id}, displayChainId=${displayChainId}, isSelected=${isSelected}, isPending=${isPending}`
          );
          return (
            <div
              key={chainOption.id}
              className={`flex items-center gap-2 p-2 cursor-pointer border-b border-border last:border-b-0 hover:bg-primary/10 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 ${
                isPending ? "opacity-70" : ""
              }`}
              onClick={() => handleSelectChain(chainOption.id)}
            >
              {/* Checkbox */}
              <div
                className={`w-4 h-4 border-2 rounded flex items-center justify-center transition-all duration-200 ${
                  isSelected
                    ? "bg-primary border-primary"
                    : "border-muted-foreground"
                }`}
              >
                {isSelected && (
                  <CheckIcon className="h-3 w-3 text-primary-foreground" />
                )}
              </div>

              {/* Chain Icon */}
              <div className="w-5 h-5 rounded-full flex items-center justify-center overflow-hidden">
                <div
                  className="w-5 h-5 rounded-full flex items-center justify-center"
                  style={{
                    backgroundColor: chainColors[chainOption.id] || "#627EEA",
                  }}
                >
                  <span className="text-white text-xs font-bold">
                    {chainName.charAt(0)}
                  </span>
                </div>
              </div>

              {/* Chain Name */}
              <span className="text-popover-foreground font-medium flex-1 text-sm">
                {chainName}
              </span>

              {/* Loading indicator for pending chain */}
              {isPending && (
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              )}
            </div>
          );
        })}
      </div>

      {/* Current Chain Footer */}
      {selectedChain && (
        <div className="p-3 border-t border-border">
          <div className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{
                backgroundColor: chainColors[selectedChain.id] || "#627EEA",
              }}
            />
            <span className="text-xs text-muted-foreground">
              Current: {getChainDisplayName(selectedChain)}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChainSelector;
