var __defProp = Object.defineProperty;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// server/nebulaClient.ts
var nebulaClient_exports = {};
__export(nebulaClient_exports, {
  generateSessionId: () => generateSessionId,
  sendNebulaMessage: () => sendNebulaMessage,
  sendNebulaMessageStream: () => sendNebulaMessageStream
});
function transformResponseForWeb3AI(message) {
  if (!message) return message;
  let transformedMessage = message.replace(/\bNebula\b/g, "Web3AI").replace(/\bnebula\b/g, "Web3AI").replace(/\bNEBULA\b/g, "WEB3AI").replace(/\bNebula's\b/g, "Web3AI's").replace(/\bnebula's\b/g, "Web3AI's").replace(/\bthirdweb's Nebula\b/g, "Web3AI").replace(/\bthirdweb Nebula\b/g, "Web3AI").replace(/\bI am Nebula\b/g, "I am Web3AI").replace(/\bI'm Nebula\b/g, "I'm Web3AI").replace(/\bNebula platform\b/g, "Web3AI platform").replace(/\bNebula AI\b/g, "Web3AI").replace(/\bNebula assistant\b/g, "Web3AI assistant");
  return transformedMessage;
}
async function sendNebulaMessage(message, sessionId, stream = false, contextFilter) {
  const secretKey = process.env.THIRDWEB_SECRET_KEY;
  if (!secretKey) {
    throw new Error("ThirdWeb secret key is required for Nebula API");
  }
  let enhancedMessage = message;
  if (contextFilter) {
    const contextParts = [];
    if (contextFilter.walletAddresses && contextFilter.walletAddresses.length > 0) {
      contextParts.push(`Wallet: ${contextFilter.walletAddresses[0]}`);
    }
    if (contextFilter.chainIds && contextFilter.chainIds.length > 0) {
      const chainNames = {
        1: "Ethereum Mainnet",
        137: "Polygon",
        56: "Binance Smart Chain",
        11155111: "Sepolia Testnet",
        80002: "Amoy Testnet"
      };
      const chainName = chainNames[contextFilter.chainIds[0]] || `Chain ${contextFilter.chainIds[0]}`;
      contextParts.push(`Network: ${chainName}`);
    }
    if (contextParts.length > 0) {
      enhancedMessage = `[Context: ${contextParts.join(", ")}] ${message}`;
    }
  }
  const payload = {
    message: enhancedMessage,
    stream
  };
  if (sessionId) {
    payload.session_id = sessionId;
  }
  try {
    const response = await fetch(`${NEBULA_API_BASE}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-secret-key": secretKey
      },
      body: JSON.stringify(payload)
    });
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }
    const data = await response.json();
    console.log("Nebula API Response:", JSON.stringify(data, null, 2));
    if (data.message) {
      data.message = transformResponseForWeb3AI(data.message);
    }
    return data;
  } catch (error) {
    console.error("Error calling Nebula API:", error);
    throw error;
  }
}
async function sendNebulaMessageStream(message, sessionId, contextFilter, onChunk) {
  console.log("sendNebulaMessageStream called with:", {
    message,
    sessionId,
    contextFilter
  });
  const secretKey = process.env.THIRDWEB_SECRET_KEY;
  console.log(
    "THIRDWEB_SECRET_KEY loaded:",
    secretKey ? "\u2713 Present" : "\u2717 Missing"
  );
  if (!secretKey) {
    throw new Error("ThirdWeb secret key is required for Nebula API");
  }
  let enhancedMessage = message;
  if (contextFilter) {
    const contextParts = [];
    if (contextFilter.walletAddresses && contextFilter.walletAddresses.length > 0) {
      contextParts.push(`Wallet: ${contextFilter.walletAddresses[0]}`);
    }
    if (contextFilter.chainIds && contextFilter.chainIds.length > 0) {
      const chainNames = {
        1: "Ethereum Mainnet",
        137: "Polygon",
        56: "Binance Smart Chain",
        11155111: "Sepolia Testnet",
        80002: "Amoy Testnet"
      };
      const chainName = chainNames[contextFilter.chainIds[0]] || `Chain ${contextFilter.chainIds[0]}`;
      contextParts.push(`Network: ${chainName}`);
    }
    if (contextParts.length > 0) {
      enhancedMessage = `[Context: ${contextParts.join(", ")}] ${message}`;
    }
  }
  const payload = {
    message: enhancedMessage,
    stream: true
  };
  if (sessionId) {
    payload.session_id = sessionId;
  }
  try {
    console.log(
      "Calling Nebula API with payload:",
      JSON.stringify(payload, null, 2)
    );
    const response = await fetch(`${NEBULA_API_BASE}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-secret-key": secretKey
      },
      body: JSON.stringify(payload)
    });
    if (!response.ok) {
      let errorText = "Unknown error";
      try {
        errorText = await response.text();
      } catch (e) {
        console.warn("Could not read error response as text:", e);
      }
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }
    const contentType = response.headers.get("content-type");
    console.log("Nebula API response content-type:", contentType);
    if (contentType?.includes("text/event-stream") || contentType?.includes("text/plain")) {
      if (!response.body) {
        throw new Error("No response body for streaming");
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";
      let finalMetadata = {};
      let fullMessage = "";
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            console.log("Sending completion metadata:", finalMetadata);
            onChunk?.("", true, finalMetadata);
            break;
          }
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";
          for (const line of lines) {
            console.log("SSE line:", line);
            if (line.startsWith("data: ")) {
              try {
                const jsonData = line.slice(6);
                if (jsonData.trim() === "[DONE]") {
                  continue;
                }
                const data = JSON.parse(jsonData);
                console.log("Parsed SSE data:", data);
                if (data.v !== void 0) {
                  const transformedChunk = transformResponseForWeb3AI(data.v);
                  fullMessage += transformedChunk;
                  onChunk?.(transformedChunk, false);
                } else if (data.type === "init") {
                  if (data.session_id)
                    finalMetadata.session_id = data.session_id;
                  if (data.request_id)
                    finalMetadata.request_id = data.request_id;
                } else if (data.choices && data.choices[0]?.delta?.content) {
                  const content = transformResponseForWeb3AI(
                    data.choices[0].delta.content
                  );
                  fullMessage += content;
                  onChunk?.(fullMessage, false);
                } else if (data.message) {
                  const transformedMessage = transformResponseForWeb3AI(
                    data.message
                  );
                  fullMessage = transformedMessage;
                  onChunk?.(transformedMessage, false);
                  if (data.session_id)
                    finalMetadata.session_id = data.session_id;
                  if (data.request_id)
                    finalMetadata.request_id = data.request_id;
                  if (data.actions) {
                    console.log("Nebula API returned actions:", data.actions);
                    finalMetadata.actions = data.actions;
                  }
                  if (data.transactions) {
                    console.log(
                      "Nebula API returned transactions:",
                      data.transactions
                    );
                    finalMetadata.transactions = data.transactions;
                  }
                } else if (data.content) {
                  const transformedContent = transformResponseForWeb3AI(
                    data.content
                  );
                  fullMessage += transformedContent;
                  onChunk?.(fullMessage, false);
                }
              } catch (parseError) {
                console.warn("Failed to parse SSE data:", line, parseError);
              }
            } else if (line.startsWith("event: ")) {
              console.log("SSE event:", line);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } else {
      const data = await response.json();
      console.log("Nebula API JSON Response:", JSON.stringify(data, null, 2));
      if (data.message) {
        data.message = transformResponseForWeb3AI(data.message);
      }
      if (data.message) {
        const words = data.message.split(" ");
        for (let i = 0; i < words.length; i++) {
          const chunk = words.slice(0, i + 1).join(" ");
          onChunk?.(chunk, false);
          await new Promise((resolve) => setTimeout(resolve, 25));
        }
      }
      const completionMetadata = {
        session_id: data.session_id,
        request_id: data.request_id,
        actions: data.actions,
        transactions: data.transactions
      };
      console.log("Sending JSON completion metadata:", completionMetadata);
      onChunk?.("", true, completionMetadata);
    }
  } catch (error) {
    console.error("Error calling Nebula streaming API:", error);
    throw error;
  }
}
function generateSessionId() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == "x" ? r : r & 3 | 8;
    return v.toString(16);
  });
}
var NEBULA_API_BASE;
var init_nebulaClient = __esm({
  "server/nebulaClient.ts"() {
    "use strict";
    NEBULA_API_BASE = "https://nebula-api.thirdweb.com";
  }
});

// server/index.ts
import dotenv2 from "dotenv";
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// server/storage.ts
import dotenv from "dotenv";

// shared/schema.ts
var schema_exports = {};
__export(schema_exports, {
  chains: () => chains,
  chats: () => chats,
  insertChainSchema: () => insertChainSchema,
  insertChatSchema: () => insertChatSchema,
  insertMessageSchema: () => insertMessageSchema,
  messages: () => messages
});
import {
  pgTable,
  text,
  serial,
  integer,
  timestamp,
  jsonb
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
var chats = pgTable("chats", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  walletAddress: text("wallet_address").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull()
});
var insertChatSchema = createInsertSchema(chats).pick({
  title: true,
  walletAddress: true
});
var messages = pgTable("messages", {
  id: serial("id").primaryKey(),
  chatId: integer("chat_id").notNull(),
  role: text("role").notNull(),
  // 'user' or 'assistant'
  content: text("content").notNull(),
  timestamp: timestamp("timestamp").defaultNow().notNull(),
  metadata: jsonb("metadata")
  // Additional data like blockchain interactions
});
var insertMessageSchema = createInsertSchema(messages).pick({
  chatId: true,
  role: true,
  content: true,
  metadata: true
});
var chains = pgTable("chains", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  chainId: text("chain_id").notNull().unique(),
  rpcUrl: text("rpc_url").notNull(),
  icon: text("icon"),
  isTestnet: text("is_testnet").default("false")
});
var insertChainSchema = createInsertSchema(chains).pick({
  name: true,
  chainId: true,
  rpcUrl: true,
  icon: true,
  isTestnet: true
});

// server/database.ts
import { drizzle } from "drizzle-orm/neon-serverless";
import { Pool } from "@neondatabase/serverless";
var db = null;
var pool = null;
function getDatabase() {
  if (!db) {
    if (!process.env.DATABASE_URL) {
      throw new Error("DATABASE_URL environment variable is required");
    }
    pool = new Pool({ connectionString: process.env.DATABASE_URL });
    db = drizzle(pool, { schema: schema_exports });
  }
  return db;
}
function closeDatabase() {
  if (pool) {
    pool.end();
    pool = null;
    db = null;
  }
}
process.on("SIGINT", () => {
  console.log("Closing database connection...");
  closeDatabase();
  process.exit(0);
});
process.on("SIGTERM", () => {
  console.log("Closing database connection...");
  closeDatabase();
  process.exit(0);
});

// server/storage.ts
import { eq, desc, asc } from "drizzle-orm";
dotenv.config();
var MemStorage = class {
  chats;
  messages;
  chains;
  chatCurrentId;
  messageCurrentId;
  chainCurrentId;
  constructor() {
    this.chats = /* @__PURE__ */ new Map();
    this.messages = /* @__PURE__ */ new Map();
    this.chains = /* @__PURE__ */ new Map();
    this.chatCurrentId = 1;
    this.messageCurrentId = 1;
    this.chainCurrentId = 1;
    this.initializeDefaultChains();
  }
  initializeDefaultChains() {
    const defaultChains = [
      {
        name: "Ethereum Mainnet",
        chainId: "1",
        rpcUrl: "https://ethereum.rpc.thirdweb.com",
        icon: "ethereum",
        isTestnet: "false"
      },
      {
        name: "Polygon",
        chainId: "137",
        rpcUrl: "https://polygon.rpc.thirdweb.com",
        icon: "polygon",
        isTestnet: "false"
      },
      {
        name: "Binance Smart Chain",
        chainId: "56",
        rpcUrl: "https://binance.rpc.thirdweb.com",
        icon: "binance",
        isTestnet: "false"
      },
      {
        name: "Goerli",
        chainId: "5",
        rpcUrl: "https://goerli.rpc.thirdweb.com",
        icon: "ethereum",
        isTestnet: "true"
      },
      {
        name: "Mumbai",
        chainId: "80001",
        rpcUrl: "https://mumbai.rpc.thirdweb.com",
        icon: "polygon",
        isTestnet: "true"
      },
      {
        name: "Sepolia",
        chainId: "11155111",
        rpcUrl: "https://sepolia.rpc.thirdweb.com",
        icon: "ethereum",
        isTestnet: "true"
      },
      {
        name: "Amoy",
        chainId: "80002",
        rpcUrl: "https://amoy.rpc.thirdweb.com",
        icon: "polygon",
        isTestnet: "true"
      }
    ];
    defaultChains.forEach((chain) => this.createChain(chain));
  }
  // Chat methods
  async getChats(walletAddress) {
    const allChats = Array.from(this.chats.values());
    const filteredChats = walletAddress ? allChats.filter((chat) => chat.walletAddress === walletAddress) : allChats;
    return filteredChats.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }
  async getChat(id, walletAddress) {
    const chat = this.chats.get(id);
    if (!chat) return void 0;
    if (walletAddress && chat.walletAddress !== walletAddress) {
      return void 0;
    }
    return chat;
  }
  async createChat(insertChat) {
    const id = this.chatCurrentId++;
    const chat = {
      ...insertChat,
      id,
      createdAt: /* @__PURE__ */ new Date()
    };
    this.chats.set(id, chat);
    return chat;
  }
  async updateChatTitle(id, title, walletAddress) {
    const chat = this.chats.get(id);
    if (!chat) return void 0;
    if (walletAddress && chat.walletAddress !== walletAddress) {
      return void 0;
    }
    const updatedChat = { ...chat, title };
    this.chats.set(id, updatedChat);
    return updatedChat;
  }
  async deleteChat(id, walletAddress) {
    const chat = this.chats.get(id);
    if (!chat) return false;
    if (walletAddress && chat.walletAddress !== walletAddress) {
      return false;
    }
    const chatMessages = Array.from(this.messages.values()).filter(
      (msg) => msg.chatId === id
    );
    chatMessages.forEach((msg) => this.messages.delete(msg.id));
    return this.chats.delete(id);
  }
  // Message methods
  async getMessages(chatId) {
    return Array.from(this.messages.values()).filter((message) => message.chatId === chatId).sort((a, b) => {
      return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
    });
  }
  async createMessage(insertMessage) {
    const id = this.messageCurrentId++;
    const message = {
      ...insertMessage,
      id,
      timestamp: /* @__PURE__ */ new Date(),
      // Make sure metadata is never undefined
      metadata: insertMessage.metadata || {}
    };
    this.messages.set(id, message);
    return message;
  }
  // Chain methods
  async getChains() {
    return Array.from(this.chains.values());
  }
  async getChain(id) {
    return this.chains.get(id);
  }
  async getChainByChainId(chainId) {
    return Array.from(this.chains.values()).find(
      (chain) => chain.chainId === chainId
    );
  }
  async createChain(insertChain) {
    const id = this.chainCurrentId++;
    const chain = {
      ...insertChain,
      id,
      icon: insertChain.icon || "",
      isTestnet: insertChain.isTestnet || "false"
    };
    this.chains.set(id, chain);
    return chain;
  }
};
var DatabaseStorage = class {
  db = getDatabase();
  // Chat methods
  async getChats(walletAddress) {
    try {
      let query = this.db.select().from(chats);
      if (walletAddress) {
        query = query.where(eq(chats.walletAddress, walletAddress));
      }
      const result = await query.orderBy(desc(chats.createdAt));
      return result;
    } catch (error) {
      console.error("Error fetching chats:", error);
      throw new Error("Failed to fetch chats");
    }
  }
  async getChat(id, walletAddress) {
    try {
      let query = this.db.select().from(chats).where(eq(chats.id, id));
      const result = await query.limit(1);
      const chat = result[0];
      if (!chat) return void 0;
      if (walletAddress && chat.walletAddress !== walletAddress) {
        return void 0;
      }
      return chat;
    } catch (error) {
      console.error("Error fetching chat:", error);
      throw new Error("Failed to fetch chat");
    }
  }
  async createChat(insertChat) {
    try {
      const result = await this.db.insert(chats).values(insertChat).returning();
      return result[0];
    } catch (error) {
      console.error("Error creating chat:", error);
      throw new Error("Failed to create chat");
    }
  }
  async updateChatTitle(id, title, walletAddress) {
    try {
      let query = this.db.update(chats).set({ title }).where(eq(chats.id, id));
      if (walletAddress) {
        query = query.where(eq(chats.walletAddress, walletAddress));
      }
      const result = await query.returning();
      return result[0];
    } catch (error) {
      console.error("Error updating chat title:", error);
      throw new Error("Failed to update chat title");
    }
  }
  async deleteChat(id, walletAddress) {
    try {
      if (walletAddress) {
        const chat = await this.getChat(id, walletAddress);
        if (!chat) {
          return false;
        }
      }
      await this.db.delete(messages).where(eq(messages.chatId, id));
      let deleteQuery = this.db.delete(chats).where(eq(chats.id, id));
      if (walletAddress) {
        deleteQuery = deleteQuery.where(eq(chats.walletAddress, walletAddress));
      }
      await deleteQuery;
      return true;
    } catch (error) {
      console.error("Error deleting chat:", error);
      throw new Error("Failed to delete chat");
    }
  }
  // Message methods
  async getMessages(chatId) {
    try {
      const result = await this.db.select().from(messages).where(eq(messages.chatId, chatId)).orderBy(asc(messages.timestamp));
      return result;
    } catch (error) {
      console.error("Error fetching messages:", error);
      throw new Error("Failed to fetch messages");
    }
  }
  async createMessage(insertMessage) {
    try {
      const result = await this.db.insert(messages).values({
        ...insertMessage,
        metadata: insertMessage.metadata || {}
      }).returning();
      return result[0];
    } catch (error) {
      console.error("Error creating message:", error);
      throw new Error("Failed to create message");
    }
  }
  // Chain methods
  async getChains() {
    try {
      const result = await this.db.select().from(chains);
      return result;
    } catch (error) {
      console.error("Error fetching chains:", error);
      throw new Error("Failed to fetch chains");
    }
  }
  async getChain(id) {
    try {
      const result = await this.db.select().from(chains).where(eq(chains.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error("Error fetching chain:", error);
      throw new Error("Failed to fetch chain");
    }
  }
  async getChainByChainId(chainId) {
    try {
      const result = await this.db.select().from(chains).where(eq(chains.chainId, chainId)).limit(1);
      return result[0];
    } catch (error) {
      console.error("Error fetching chain by chainId:", error);
      throw new Error("Failed to fetch chain");
    }
  }
  async createChain(insertChain) {
    try {
      const result = await this.db.insert(chains).values({
        ...insertChain,
        icon: insertChain.icon || "",
        isTestnet: insertChain.isTestnet || "false"
      }).returning();
      return result[0];
    } catch (error) {
      console.error("Error creating chain:", error);
      throw new Error("Failed to create chain");
    }
  }
};
var storage = process.env.DATABASE_URL ? new DatabaseStorage() : new MemStorage();

// server/llm.ts
init_nebulaClient();
function transformResponseForWeb3AI2(message) {
  if (!message) return message;
  let transformedMessage = message.replace(/\bNebula\b/g, "Web3AI").replace(/\bnebula\b/g, "Web3AI").replace(/\bNEBULA\b/g, "WEB3AI").replace(/\bNebula's\b/g, "Web3AI's").replace(/\bnebula's\b/g, "Web3AI's").replace(/\bthirdweb's Nebula\b/g, "Web3AI").replace(/\bthirdweb Nebula\b/g, "Web3AI").replace(/\bI am Nebula\b/g, "I am Web3AI").replace(/\bI'm Nebula\b/g, "I'm Web3AI").replace(/\bNebula platform\b/g, "Web3AI platform").replace(/\bNebula AI\b/g, "Web3AI").replace(/\bNebula assistant\b/g, "Web3AI assistant");
  return transformedMessage;
}
async function generateChatResponse(chatHistory, chainId, sessionId, walletAddress) {
  const lastUserMessage = [...chatHistory].reverse().find((msg) => msg.role === "user");
  if (!lastUserMessage) {
    throw new Error("No user message found in history");
  }
  const startTime = Date.now();
  const isFallbackSession = sessionId.startsWith("fallback_");
  try {
    if (!isFallbackSession) {
      const enhancedMessage = `On ${getChainName(chainId)} network: ${lastUserMessage.content}`;
      const contextFilter = {
        chainIds: [parseInt(chainId)]
      };
      if (walletAddress) {
        contextFilter.walletAddresses = [walletAddress];
      }
      const nebulaResponse = await sendNebulaMessage(
        enhancedMessage,
        sessionId,
        // Use the provided session ID
        false,
        contextFilter
      );
      const executionTime = Date.now() - startTime;
      const transformedMessage = transformResponseForWeb3AI2(
        nebulaResponse.message || "Response received from Web3AI"
      );
      return {
        text: transformedMessage,
        source: `Web3AI on ${getChainName(chainId)}`,
        executionTime,
        blockchainData: {
          sessionId: nebulaResponse.session_id,
          chainId,
          enhanced: true,
          actions: nebulaResponse.actions || [],
          transactions: nebulaResponse.transactions || []
        }
      };
    } else {
      throw new Error("Using fallback session - skipping Nebula API");
    }
  } catch (error) {
    console.error("Error calling Nebula API or using fallback session:", error);
    const executionTime = Date.now() - startTime;
    return generateFallbackResponse(
      lastUserMessage.content,
      chainId,
      executionTime
    );
  }
}
function generateFallbackResponse(query, chainId, fallbackExecutionTime) {
  const userQuery = query.toLowerCase();
  let responseText = "";
  let blockchainData = null;
  if (userQuery.includes("gas") || userQuery.includes("fee")) {
    responseText = createGasFeeResponse(chainId);
    blockchainData = {
      low: 35,
      average: 42,
      high: 55,
      unit: "Gwei"
    };
  } else if (userQuery.includes("launch") && userQuery.includes("token")) {
    responseText = createTokenLaunchResponse(chainId);
  } else if (userQuery.includes("buy") && userQuery.includes("usdc")) {
    responseText = createBuyUSDCResponse(chainId);
  } else if (userQuery.includes("analyze") || userQuery.includes("contract")) {
    responseText = createContractAnalysisResponse();
  } else if (userQuery.includes("send") && (userQuery.includes("eth") || userQuery.includes("token"))) {
    responseText = createSendTokenResponse(chainId);
  } else if (userQuery.includes("what") && userQuery.includes("do")) {
    responseText = createCapabilitiesResponse();
  } else {
    responseText = createGenericResponse(userQuery);
  }
  return {
    text: responseText,
    source: `Enhanced Assistant on ${getChainName(chainId)}`,
    executionTime: fallbackExecutionTime,
    blockchainData
  };
}
function getChainName(chainId) {
  const chainNames = {
    "1": "Ethereum Mainnet",
    "137": "Polygon",
    "56": "Binance Smart Chain",
    "5": "Goerli Testnet",
    "80001": "Mumbai Testnet"
  };
  return chainNames[chainId] || "Unknown Chain";
}
function createGasFeeResponse(chainId) {
  return `To check current gas fees on ${getChainName(
    chainId
  )}, you have several options:

1. Use blockchain explorers like Etherscan, which shows real-time gas prices
2. Check gas tracking websites like ETH Gas Station or Gas Now
3. Use wallet applications (MetaMask shows current gas estimates)
4. Call the Ethereum JSON-RPC method \`eth_gasPrice\`

Current ${getChainName(chainId)} Gas Prices:
- Low: 35 Gwei
- Average: 42 Gwei
- High: 55 Gwei

Would you like me to help you estimate gas for a specific transaction type?`;
}
function createTokenLaunchResponse(chainId) {
  return `Launching a token on ${getChainName(chainId)} involves several steps:

1. **Create the Token Contract**: You can use thirdweb's TokenDrop or Token contract templates for this.

2. **Deploy the Contract**: Use the thirdweb dashboard or SDK to deploy to ${getChainName(
    chainId
  )}.

\`\`\`javascript
// Example code to deploy a token with thirdweb SDK
import { ThirdwebSDK } from "@thirdweb-dev/sdk";

const sdk = ThirdwebSDK.fromPrivateKey(
  process.env.PRIVATE_KEY,
  "${chainId}" // ${getChainName(chainId)}
);

const tokenContract = await sdk.deployer.deployToken({
  name: "My Token",
  symbol: "MTK",
  primary_sale_recipient: "0x...",
});

console.log("Token contract deployed at:", tokenContract.getAddress());
\`\`\`

3. **Configure Token Properties**: Set supply, minting rules, etc.
4. **Add Liquidity**: Create trading pairs on DEXs
5. **Verify the Contract**: Ensure your code is verified on block explorers

Would you like me to guide you through any specific step in more detail?`;
}
function createBuyUSDCResponse(chainId) {
  return `To buy USDC on ${getChainName(chainId)}, you have several options:

1. **Centralized Exchanges**: Exchanges like Coinbase, Binance, or Kraken allow direct purchases of USDC with fiat currencies.

2. **Decentralized Exchanges (DEXs)**: You can swap other cryptocurrencies for USDC on:
   - Uniswap
   - SushiSwap
   - Curve Finance

3. **Using thirdweb SDK**: You can programmatically swap for USDC with code:

\`\`\`javascript
import { ThirdwebSDK } from "@thirdweb-dev/sdk";

// Initialize SDK
const sdk = new ThirdwebSDK("${chainId}");

// Connect wallet
const wallet = await sdk.wallet.connect();

// Get the ERC20 contract for USDC
const usdcAddress = "******************************************"; // USDC on Ethereum
const usdcContract = await sdk.getContract(usdcAddress);

// Swap ETH for USDC via router
const routerAddress = "0x..."; // DEX router address
const routerContract = await sdk.getContract(routerAddress);

// Approve the transaction
await routerContract.call("swapExactETHForTokens", {
  value: ethers.utils.parseEther("0.1"), // Amount of ETH to swap
  // Other parameters like minimum amount out, path, recipient, deadline
});
\`\`\`

Would you like step-by-step instructions for a specific method?`;
}
function createContractAnalysisResponse() {
  return `To analyze Uniswap contracts, you can:

1. **Fetch the Contract ABI**: This can be done using block explorers or thirdweb

2. **Analyze Core Functions**: Key Uniswap contracts include:
   - Factory contract: Creates pairs
   - Router contract: Handles trading transactions
   - Pair contracts: Individual trading pairs

3. **Using thirdweb SDK**:

\`\`\`javascript
import { ThirdwebSDK } from "@thirdweb-dev/sdk";

// Initialize the SDK
const sdk = new ThirdwebSDK("ethereum");

// Get Uniswap V2 Factory contract
const factoryAddress = "******************************************";
const factoryContract = await sdk.getContract(factoryAddress);

// Get all created pairs
const allPairsLength = await factoryContract.call("allPairsLength");
console.log("Total Uniswap V2 pairs:", allPairsLength);

// Get a specific pair
const pairAddress = await factoryContract.call("getPair", [
  "0xTokenA", // Token A address
  "0xTokenB"  // Token B address
]);

// Get pair contract and analyze
const pairContract = await sdk.getContract(pairAddress);
const reserves = await pairContract.call("getReserves");
console.log("Pair reserves:", reserves);
\`\`\`

Would you like me to analyze a specific aspect of Uniswap contracts?`;
}
function createSendTokenResponse(chainId) {
  return `To send ETH to someone on ${getChainName(chainId)}, you can:

1. **Using a Wallet**: The simplest way is to use MetaMask, Trust Wallet, etc.

2. **Using thirdweb SDK**:

\`\`\`javascript
import { ThirdwebSDK } from "@thirdweb-dev/sdk";
import { ethers } from "ethers";

// Initialize the SDK with your private key (server-side) or with Web3 provider (client-side)
const sdk = ThirdwebSDK.fromPrivateKey(
  process.env.PRIVATE_KEY, // Your private key
  "${chainId}" // ${getChainName(chainId)}
);

// For client-side with connected wallet
// const sdk = new ThirdwebSDK("${chainId}");
// await sdk.wallet.connect();

// Send ETH
const recipientAddress = "0x..."; // Recipient's address
const amountInEther = "0.1"; // Amount to send

// Convert amount to wei (smallest unit)
const amountInWei = ethers.utils.parseEther(amountInEther);

// Send the transaction
const tx = await sdk.wallet.transfer(
  recipientAddress,
  amountInWei
);

console.log("Transaction hash:", tx.receipt.transactionHash);
\`\`\`

3. **For ERC-20 Tokens**:

\`\`\`javascript
// Get the token contract
const tokenAddress = "0x..."; // Token contract address
const tokenContract = await sdk.getContract(tokenAddress);

// Transfer tokens
await tokenContract.erc20.transfer(
  recipientAddress,
  amountToSend
);
\`\`\`

Would you like more details on any of these methods?`;
}
function createCapabilitiesResponse() {
  return `I'm Web3AI, your intelligent blockchain assistant! Here's what I can help you with:

1. **Smart Contract Interactions**:
   - Deploy and interact with contracts
   - Analyze contract code
   - Troubleshoot contract issues

2. **Wallet Operations**:
   - Help connect wallets
   - Assist with transfers
   - Explain gas fees and optimize transactions

3. **Token Actions**:
   - Guide you through token launches
   - Help with token swaps and purchases
   - Explain tokenomics concepts

4. **Development Support**:
   - Provide code snippets for blockchain interactions
   - Explain web3 development concepts
   - Help debug web3 applications

5. **Chain Information**:
   - Provide data about different blockchains
   - Track gas prices and network conditions
   - Compare chain features and compatibility

Just ask me questions about any blockchain topic, and I'll assist you with information, code examples, and actionable guidance!`;
}
function createGenericResponse(query) {
  return `I understand you're asking about "${query}". To help you with blockchain-related queries, I can:

1. Provide information about various blockchain networks
2. Help with smart contract deployment and interaction
3. Assist with wallet operations like sending tokens
4. Explain web3 concepts and development approaches
5. Generate code examples using thirdweb SDK

Could you provide more specific details about what you'd like to know about "${query}" in the blockchain context?`;
}

// server/routes.ts
import { ZodError } from "zod";

// server/chatTitleGenerator.ts
function generateChatTitle(firstMessage) {
  const cleanMessage = firstMessage.trim();
  if (cleanMessage.length < 3) {
    return "New Chat";
  }
  const prefixesToRemove = [
    "hey",
    "hi",
    "hello",
    "can you",
    "could you",
    "please",
    "i want to",
    "i need to",
    "help me",
    "how do i",
    "how to",
    "what is",
    "what are",
    "tell me",
    "explain"
  ];
  let title = cleanMessage.toLowerCase();
  for (const prefix of prefixesToRemove) {
    if (title.startsWith(prefix)) {
      title = title.substring(prefix.length).trim();
      break;
    }
  }
  if (title.length === 0) {
    title = cleanMessage;
  }
  title = title.charAt(0).toUpperCase() + title.slice(1);
  const maxLength = 50;
  if (title.length > maxLength) {
    title = title.substring(0, maxLength).trim();
    const lastSpace = title.lastIndexOf(" ");
    if (lastSpace > maxLength * 0.7) {
      title = title.substring(0, lastSpace);
    }
    title += "...";
  }
  title = title.replace(/[,;:!]+$/, "");
  if (!title.match(/[.?]$/)) {
    if (title.toLowerCase().includes("how") || title.toLowerCase().includes("what") || title.toLowerCase().includes("why") || title.toLowerCase().includes("when") || title.toLowerCase().includes("where")) {
    } else {
      title += "";
    }
  }
  return title;
}
function generateWeb3ChatTitle(message) {
  const lowerMessage = message.toLowerCase();
  const web3Patterns = [
    { pattern: /swap.*token/i, title: "Token Swap" },
    { pattern: /check.*balance/i, title: "Check Balance" },
    { pattern: /send.*eth|transfer.*eth/i, title: "Send ETH" },
    { pattern: /deploy.*contract/i, title: "Deploy Contract" },
    { pattern: /mint.*nft/i, title: "Mint NFT" },
    { pattern: /stake.*token/i, title: "Stake Tokens" },
    { pattern: /bridge.*token/i, title: "Bridge Tokens" },
    { pattern: /gas.*fee/i, title: "Gas Fees" },
    { pattern: /defi.*protocol/i, title: "DeFi Protocol" },
    { pattern: /yield.*farm/i, title: "Yield Farming" },
    { pattern: /liquidity.*pool/i, title: "Liquidity Pool" },
    { pattern: /smart.*contract/i, title: "Smart Contract" },
    { pattern: /metamask/i, title: "MetaMask Help" },
    { pattern: /wallet.*connect/i, title: "Wallet Connection" },
    { pattern: /transaction.*fail/i, title: "Transaction Failed" },
    { pattern: /approve.*token/i, title: "Token Approval" }
  ];
  for (const { pattern, title } of web3Patterns) {
    if (pattern.test(message)) {
      return title;
    }
  }
  return generateChatTitle(message);
}

// server/routes.ts
async function registerRoutes(app2) {
  app2.post("/api/rpc/:chainId", async (req, res) => {
    try {
      const { chainId } = req.params;
      const getRPCEndpoint = (chainId2) => {
        const alchemyApiKey = process.env.ALCHEMY_API_KEY;
        if (!alchemyApiKey) {
          console.warn("ALCHEMY_API_KEY not found, using public RPC endpoints");
          const publicEndpoints = {
            "1": "https://eth.llamarpc.com",
            "137": "https://polygon.llamarpc.com",
            "56": "https://bsc-dataseed.binance.org/",
            "43114": "https://api.avax.network/ext/bc/C/rpc",
            "250": "https://rpc.ftm.tools/",
            "42161": "https://arb1.arbitrum.io/rpc",
            "10": "https://mainnet.optimism.io",
            "8453": "https://mainnet.base.org",
            "11155111": "https://eth-sepolia.public.blastapi.io",
            // Sepolia
            "80002": "https://rpc-amoy.polygon.technology"
            // Amoy
          };
          return publicEndpoints[chainId2];
        }
        const endpoints = {
          // Ethereum Networks
          "1": `https://eth-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "11155111": `https://eth-sepolia.g.alchemy.com/v2/${alchemyApiKey}`,
          // Sepolia
          // Polygon Networks
          "137": `https://polygon-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "80002": `https://polygon-amoy.g.alchemy.com/v2/${alchemyApiKey}`,
          // Amoy
          // Arbitrum Networks
          "42161": `https://arb-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "421614": `https://arb-sepolia.g.alchemy.com/v2/${alchemyApiKey}`,
          // Arbitrum Sepolia
          // Optimism Networks
          "10": `https://opt-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "11155420": `https://opt-sepolia.g.alchemy.com/v2/${alchemyApiKey}`,
          // Optimism Sepolia
          // Base Networks
          "8453": `https://base-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "84532": `https://base-sepolia.g.alchemy.com/v2/${alchemyApiKey}`,
          // Base Sepolia
          // Other networks (using public endpoints as Alchemy doesn't support all)
          "56": "https://bsc-dataseed.binance.org/",
          // BSC
          "97": "https://data-seed-prebsc-1-s1.binance.org:8545/",
          // BSC Testnet
          "43114": "https://api.avax.network/ext/bc/C/rpc",
          // Avalanche
          "43113": "https://api.avax-test.network/ext/bc/C/rpc",
          // Avalanche Fuji
          "250": "https://rpc.ftm.tools/",
          // Fantom
          "4002": "https://rpc.testnet.fantom.network/",
          // Fantom Testnet
          "25": "https://evm.cronos.org",
          // Cronos
          "338": "https://evm-t3.cronos.org",
          // Cronos Testnet
          "1284": "https://rpc.api.moonbeam.network",
          // Moonbeam
          "1287": "https://rpc.api.moonbase.moonbeam.network",
          // Moonbase Alpha
          "42220": "https://forno.celo.org",
          // Celo
          "44787": "https://alfajores-forno.celo-testnet.org"
          // Celo Alfajores
        };
        return endpoints[chainId2];
      };
      const rpcEndpoint = getRPCEndpoint(chainId);
      if (!rpcEndpoint) {
        return res.status(400).json({
          error: `Unsupported chainId: ${chainId}`,
          supportedChains: {
            "1": "Ethereum",
            "137": "Polygon",
            "56": "BSC",
            "42161": "Arbitrum",
            "10": "Optimism",
            "43114": "Avalanche",
            "8453": "Base",
            "250": "Fantom",
            "25": "Cronos",
            "1284": "Moonbeam",
            "42220": "Celo"
          }
        });
      }
      const response = await fetch(rpcEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(req.body)
      });
      if (!response.ok) {
        throw new Error(`RPC request failed: ${response.status}`);
      }
      const data = await response.json();
      res.json(data);
    } catch (error) {
      console.error("RPC proxy error:", error);
      res.status(500).json({
        error: "RPC request failed",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });
  app2.post("/api/nebula/create-session", async (req, res) => {
    try {
      const { sendNebulaMessage: sendNebulaMessage2 } = await Promise.resolve().then(() => (init_nebulaClient(), nebulaClient_exports));
      const nebulaResponse = await sendNebulaMessage2(
        "Hello, I am ready to chat about blockchain development!",
        void 0,
        // Let Nebula create new session
        false
      );
      return res.json({
        sessionId: nebulaResponse.session_id,
        message: "Session created successfully"
      });
    } catch (error) {
      console.error("Failed to create Nebula session:", error);
      return res.status(500).json({ error: "Failed to create Nebula session" });
    }
  });
  app2.post("/api/nebula/chat", async (req, res) => {
    try {
      const { sendNebulaMessage: sendNebulaMessage2 } = await Promise.resolve().then(() => (init_nebulaClient(), nebulaClient_exports));
      const { message, session_id, stream, contextFilter } = req.body;
      if (!message) {
        return res.status(400).json({ error: "Message is required" });
      }
      const nebulaResponse = await sendNebulaMessage2(
        message,
        session_id,
        stream || false,
        contextFilter
      );
      return res.json({
        response: nebulaResponse.message,
        session_id: nebulaResponse.session_id,
        request_id: nebulaResponse.request_id,
        actions: nebulaResponse.actions,
        transactions: nebulaResponse.transactions
      });
    } catch (error) {
      console.error("Failed to send Nebula message:", error);
      return res.status(500).json({ error: "Failed to send message to Nebula" });
    }
  });
  app2.post("/api/nebula/chat/stream", async (req, res) => {
    console.log("Streaming endpoint called with body:", req.body);
    try {
      const { sendNebulaMessageStream: sendNebulaMessageStream2 } = await Promise.resolve().then(() => (init_nebulaClient(), nebulaClient_exports));
      const { message, session_id, contextFilter } = req.body;
      if (!message) {
        return res.status(400).json({ error: "Message is required" });
      }
      res.writeHead(200, {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control"
      });
      try {
        await sendNebulaMessageStream2(
          message,
          session_id,
          contextFilter,
          (chunk, isComplete, metadata) => {
            if (isComplete) {
              res.write(
                `data: ${JSON.stringify({
                  type: "complete",
                  session_id: metadata?.session_id,
                  request_id: metadata?.request_id,
                  actions: metadata?.actions,
                  transactions: metadata?.transactions
                })}

`
              );
              res.end();
            } else {
              res.write(
                `data: ${JSON.stringify({
                  type: "chunk",
                  content: chunk
                })}

`
              );
            }
          }
        );
      } catch (error) {
        console.error("Streaming error:", error);
        res.write(
          `data: ${JSON.stringify({
            type: "error",
            error: "Streaming failed"
          })}

`
        );
        res.end();
      }
    } catch (error) {
      console.error("Failed to send streaming Nebula message:", error);
      res.write(
        `data: ${JSON.stringify({
          type: "error",
          error: "Failed to send message to Nebula"
        })}

`
      );
      res.end();
    }
  });
  app2.get("/api/chats", async (req, res) => {
    try {
      const walletAddress = req.query.walletAddress;
      const chats2 = await storage.getChats(walletAddress);
      return res.json(chats2);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch chats" });
    }
  });
  app2.post("/api/chats", async (req, res) => {
    try {
      const parsedData = insertChatSchema.parse(req.body);
      const chat = await storage.createChat(parsedData);
      return res.status(201).json(chat);
    } catch (error) {
      if (error instanceof ZodError) {
        return res.status(400).json({ message: "Invalid chat data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create chat" });
    }
  });
  app2.delete("/api/chats/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      const walletAddress = req.query.walletAddress;
      const success = await storage.deleteChat(id, walletAddress);
      if (!success) {
        return res.status(404).json({ message: "Chat not found or access denied" });
      }
      return res.status(200).json({ message: "Chat deleted" });
    } catch (error) {
      return res.status(500).json({ message: "Failed to delete chat" });
    }
  });
  app2.get("/api/chats/:id/messages", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      const walletAddress = req.query.walletAddress;
      const chat = await storage.getChat(id, walletAddress);
      if (!chat) {
        return res.status(404).json({ message: "Chat not found or access denied" });
      }
      const messages2 = await storage.getMessages(id);
      return res.json(messages2);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch messages" });
    }
  });
  app2.post("/api/chats/:id/messages", async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      const {
        walletAddress,
        metadata,
        sessionId: requestSessionId,
        account
      } = req.body;
      const chat = await storage.getChat(chatId, walletAddress);
      if (!chat) {
        return res.status(404).json({ message: "Chat not found or access denied" });
      }
      const messageData = { ...req.body, chatId, role: "user" };
      const parsedData = insertMessageSchema.parse(messageData);
      const userMessage = await storage.createMessage(parsedData);
      const chatHistory = await storage.getMessages(chatId);
      if (chatHistory.length === 1) {
        const newTitle = generateWeb3ChatTitle(parsedData.content);
        await storage.updateChatTitle(chatId, newTitle, walletAddress);
      }
      const chainId = metadata?.chainId || "1";
      let sessionId = requestSessionId;
      if (!sessionId) {
        try {
          const { sendNebulaMessage: sendNebulaMessage2 } = await Promise.resolve().then(() => (init_nebulaClient(), nebulaClient_exports));
          const nebulaResponse = await sendNebulaMessage2(
            "Hello, I am ready to chat about blockchain development!",
            void 0,
            // Let Nebula create new session
            false
          );
          sessionId = nebulaResponse.session_id;
          console.log("Created new Nebula session:", sessionId);
        } catch (error) {
          console.error("Failed to create Nebula session:", error);
          sessionId = `fallback_${Date.now()}`;
        }
      }
      try {
        const aiResponse = await generateChatResponse(
          chatHistory,
          chainId,
          sessionId,
          walletAddress
        );
        const assistantMessageData = {
          chatId,
          role: "assistant",
          content: aiResponse.text,
          metadata: {
            chainId,
            source: aiResponse.source,
            executionTime: aiResponse.executionTime,
            blockchainData: aiResponse.blockchainData
          }
        };
        const assistantMessage = await storage.createMessage(
          assistantMessageData
        );
        return res.status(201).json({
          userMessage,
          assistantMessage
        });
      } catch (error) {
        return res.status(500).json({
          userMessage,
          error: "Failed to generate assistant response"
        });
      }
    } catch (error) {
      if (error instanceof ZodError) {
        return res.status(400).json({ message: "Invalid message data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to process message" });
    }
  });
  app2.get("/api/chains", async (_req, res) => {
    try {
      const chains2 = await storage.getChains();
      return res.json(chains2);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch chains" });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/initDatabase.ts
import { sql } from "drizzle-orm";
import fs2 from "fs";
import path3 from "path";
async function initializeDatabase() {
  const db2 = getDatabase();
  try {
    console.log("Initializing database...");
    const migrationPath = path3.join(
      process.cwd(),
      "migrations",
      "0001_initial.sql"
    );
    if (fs2.existsSync(migrationPath)) {
      const migrationSQL = fs2.readFileSync(migrationPath, "utf8");
      const statements = migrationSQL.split(";").map((stmt) => stmt.trim()).filter((stmt) => stmt.length > 0);
      for (const statement of statements) {
        try {
          await db2.execute(sql.raw(statement));
        } catch (error) {
          if (!error.message?.includes("already exists") && !error.message?.includes("duplicate key")) {
            console.error("Error executing statement:", statement);
            throw error;
          }
        }
      }
      console.log("Database initialized successfully");
    } else {
      console.log("No migration file found, skipping database initialization");
    }
  } catch (error) {
    console.error("Failed to initialize database:", error);
    throw error;
  }
}
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeDatabase().then(() => {
    console.log("Database initialization completed");
    process.exit(0);
  }).catch((error) => {
    console.error("Database initialization failed:", error);
    process.exit(1);
  });
}

// server/index.ts
dotenv2.config();
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path4 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path4.startsWith("/api")) {
      let logLine = `${req.method} ${path4} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  if (process.env.DATABASE_URL) {
    try {
      await initializeDatabase();
      log("Database initialized successfully");
    } catch (error) {
      log("Failed to initialize database:", String(error));
    }
  } else {
    log("No DATABASE_URL provided, using in-memory storage");
  }
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5e3;
  server.listen(
    {
      port,
      host: "0.0.0.0",
      reusePort: true
    },
    () => {
      log(`serving on port ${port}`);
    }
  );
})();
