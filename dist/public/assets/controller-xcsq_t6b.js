const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es-BSVo9bMa.js","assets/index-S7-aQ7dt.js","assets/index-BTdDFzwe.css"])))=>i.map(i=>d[i]);
import{g as W,n as D,a as p,b as R,_ as Q,D as z,c as v,d as J,e as M,f as x,s as K,h as S,i as Y,j as q,t as B,k as G,u as X,p as Z,l as $,v as O,m as nn,N as k,o as an}from"./index-S7-aQ7dt.js";import{U as en,S as tn}from"./rpc-DfbEpyiO.js";const b="wallet_addEthereumChain",sn=!0,P={requestedChains:"tw.wc.requestedChains",lastUsedChainId:"tw.wc.lastUsedChainId"};async function un(n,s,t,c,e){var g,y;const a=await F(n,t,e),i=n.walletConnect;let{onDisplayUri:o}=i||{};if(!o&&e){const f=await R(t);o=m=>{const w=f.mobile.native||f.mobile.universal;if(!w){e(m);return}const E=an(w,m).redirect;e(E)}}o&&a.events.addListener("display_uri",o);let d=i==null?void 0:i.optionalChains,r=n.chain;t==="global.safe"&&(d=V.map(p),r&&!d.includes(r)&&(r=void 0));const{rpcMap:h,requiredChain:l,optionalChains:u}=j({client:n.client,chain:r,optionalChains:d});a.session&&await a.connect({...i!=null&&i.pairingTopic?{pairingTopic:i==null?void 0:i.pairingTopic}:{},optionalChains:u,chains:l?[l.id]:void 0,rpcMap:h}),A(u,c);const C=(await a.enable())[0];if(!C)throw new Error("No accounts found on provider.");const T=D(a.chainId),_=n.chain&&n.chain.id===T?n.chain:p(T);if(n){const f={optionalChains:(g=n.walletConnect)==null?void 0:g.optionalChains,chain:n.chain,pairingTopic:(y=n.walletConnect)==null?void 0:y.pairingTopic};c&&Y(c,t,f)}return i!=null&&i.onDisplayUri&&a.events.removeListener("display_uri",i.onDisplayUri),H(C,_,a,s,c,n.client)}async function Cn(n,s,t,c,e){const a=c?await W(c,t):null,i=await F(a?{chain:a.chain,client:n.client,walletConnect:{pairingTopic:a.pairingTopic,optionalChains:a.optionalChains}}:{client:n.client,walletConnect:{}},t,e,!0),o=i.accounts[0];if(!o)throw new Error("No accounts found on provider.");const d=D(i.chainId),r=n.chain&&n.chain.id===d?n.chain:p(d);return H(o,r,i,s,c,n.client)}async function F(n,s,t,c=!1){var T,_,g,y;const e=await R(s),a=n.walletConnect,{EthereumProvider:i,OPTIONAL_EVENTS:o,OPTIONAL_METHODS:d}=await Q(async()=>{const{EthereumProvider:f,OPTIONAL_EVENTS:I,OPTIONAL_METHODS:m}=await import("./index.es-BSVo9bMa.js");return{EthereumProvider:f,OPTIONAL_EVENTS:I,OPTIONAL_METHODS:m}},__vite__mapDeps([0,1,2]));let r=a==null?void 0:a.optionalChains,h=n.chain;s==="global.safe"&&(r=V.map(p),h&&!r.includes(h)&&(h=void 0));const{rpcMap:l,requiredChain:u,optionalChains:U}=j({client:n.client,chain:h,optionalChains:r}),C=await i.init({showQrModal:(a==null?void 0:a.showQrModal)===void 0?t?!1:sn:a.showQrModal,projectId:(a==null?void 0:a.projectId)||z,optionalMethods:d,optionalEvents:o,optionalChains:U,chains:u?[u.id]:void 0,metadata:{name:((T=a==null?void 0:a.appMetadata)==null?void 0:T.name)||v().name,description:((_=a==null?void 0:a.appMetadata)==null?void 0:_.description)||v().description,url:((g=a==null?void 0:a.appMetadata)==null?void 0:g.url)||v().url,icons:[((y=a==null?void 0:a.appMetadata)==null?void 0:y.logoUrl)||v().logoUrl]},rpcMap:l,qrModalOptions:a==null?void 0:a.qrModalOptions,disableProviderPing:!0});if(C.events.setMaxListeners(Number.POSITIVE_INFINITY),c||C.session&&await C.disconnect(),s!=="walletConnect"){async function f(){var m,w,E,N;const I=((N=(E=(w=(m=C.session)==null?void 0:m.peer)==null?void 0:w.metadata)==null?void 0:E.redirect)==null?void 0:N.native)||e.mobile.native||e.mobile.universal;t&&I&&await t(I)}C.signer.client.on("session_request_sent",f),C.events.addListener("disconnect",()=>{C.signer.client.off("session_request_sent",f)})}return C}function L({provider:n,address:s,client:t}){return{address:q(s),async sendTransaction(e){const a=await n.request({method:"eth_sendTransaction",params:[{gas:e.gas?M(e.gas):void 0,value:e.value?M(e.value):void 0,from:q(s),to:e.to,data:e.data}]});return B({client:t,walletAddress:q(s),walletType:"walletConnect",transactionHash:a,chainId:e.chainId,contractAddress:e.to??void 0,gasPrice:e.gasPrice}),{transactionHash:a}},async signMessage({message:e}){const a=typeof e=="string"?G(e):e.raw instanceof Uint8Array?X(e.raw):e.raw;return n.request({method:"personal_sign",params:[a,this.address]})},async signTypedData(e){const a=Z(e),{domain:i,message:o,primaryType:d}=a,r={EIP712Domain:$({domain:i}),...a.types};O({domain:i,message:o,primaryType:d,types:r});const h=nn({domain:i??{},message:o,primaryType:d,types:r});return await n.request({method:"eth_signTypedData_v4",params:[this.address,h]})}}}function H(n,s,t,c,e,a){const i=L({provider:t,address:n,client:a});async function o(){t.removeListener("accountsChanged",r),t.removeListener("chainChanged",h),t.removeListener("disconnect",d),await t.disconnect()}function d(){A([],e),e==null||e.removeItem(P.lastUsedChainId),o(),c.emit("disconnect",void 0)}function r(l){if(l[0]){const u=L({provider:t,address:q(l[0]),client:a});c.emit("accountChanged",u),c.emit("accountsChanged",l)}else d()}function h(l){const u=p(D(l));c.emit("chainChanged",u),e==null||e.setItem(P.lastUsedChainId,String(l))}return t.on("accountsChanged",r),t.on("chainChanged",h),t.on("disconnect",d),t.on("session_delete",d),[i,s,o,l=>on(t,l,e)]}function cn(n){var s,t;return((t=(s=n.session)==null?void 0:s.namespaces[k])==null?void 0:t.methods)||[]}function rn(n){var t,c,e;return((e=(c=(t=n.session)==null?void 0:t.namespaces[k])==null?void 0:c.chains)==null?void 0:e.map(a=>Number.parseInt(a.split(":")[1]||"")))??[]}async function on(n,s,t){var e,a;const c=s.id;try{const i=rn(n),o=cn(n);if(!i.includes(c)&&o.includes(b)){const r=await J(s),h=[...new Set([...((e=s.blockExplorers)==null?void 0:e.map(u=>u.url))||[],...((a=r.explorers)==null?void 0:a.map(u=>u.url))||[]])];await n.request({method:b,params:[{chainId:M(r.chainId),chainName:r.name,nativeCurrency:r.nativeCurrency,rpcUrls:x(r),blockExplorerUrls:h.length>0?h:void 0}]});const l=await dn(t);l.push(c),A(l,t)}await n.request({method:"wallet_switchEthereumChain",params:[{chainId:M(c)}]})}catch(i){const o=typeof i=="string"?i:i==null?void 0:i.message;throw/user rejected request/i.test(o)?new en(i):new tn(i)}}function A(n,s){s==null||s.setItem(P.requestedChains,K(n))}async function dn(n){const s=await n.getItem(P.requestedChains);return s?JSON.parse(s):[]}function j(n){const s={};n.chain&&(s[n.chain.id]=S({chain:n.chain,client:n.client}));const t=((n==null?void 0:n.optionalChains)||[]).slice(0,10);for(const c of t)s[c.id]=S({chain:c,client:n.client});return!n.chain&&t.length===0&&(s[1]=p(1).rpc),{rpcMap:s,requiredChain:n.chain?n.chain:void 0,optionalChains:t.length>0?t.map(c=>c.id):[1]}}const V=[1,11155111,42161,43114,8453,1313161554,84532,56,42220,100,10,137,1101,324,534352];export{Cn as autoConnectWC,un as connectWC};
