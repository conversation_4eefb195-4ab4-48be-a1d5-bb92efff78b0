import{e as _}from"./eth_sendRawTransaction-DPdnXbFR.js";import{ax as x,aA as l,bb as L,u as v,bc as c,aN as B,j as z,bd as D,be as y,bf as S,aw as M}from"./index-62UCgHi7.js";import{concatHex as A}from"./concat-hex-BBACT9RW.js";import{m as $,s as O,t as H}from"./sha256-Dkiv-shS.js";const b=$*32n;class W extends x{constructor({givenLength:e,maxBytecodeSize:a}){super(`Bytecode cannot be longer than ${a} bytes. Given length: ${e}`,{name:"BytecodeLengthExceedsMaxSizeError"})}}class k extends x{constructor({givenLengthInWords:e}){super(`Bytecode length in 32-byte words must be odd. Given length in words: ${e}`,{name:"BytecodeLengthInWordsMustBeOddError"})}}class N extends x{constructor({givenLength:e}){super(`The bytecode length in bytes must be divisible by 32. Given length: ${e}`,{name:"BytecodeLengthMustBeDivisibleBy32Error"})}}function R(t){const e=l(t);if(e.length%32!==0)throw new N({givenLength:e.length});if(e.length>b)throw new W({givenLength:e.length,maxBytecodeSize:b});const a=O(e),n=l(a),r=e.length/32;if(r%2===0)throw new k({givenLengthInWords:r});const i=l(r),s=L(i,{size:2}),o=new Uint8Array([1,0]);return n.set(o,0),n.set(s,2),n}function h(t){if(["string","number"].includes(typeof t)&&!Number.isInteger(Number(t)))throw new Error(`Expected value to be an integer to convert to a bigint, got ${t} of type ${typeof t}`);return t instanceof Uint8Array?BigInt(v(t)):BigInt(t)}const P=(t,e)=>typeof t=="bigint"?e(t):Array.isArray(t)?t.map(a=>P(a,e)):t&&typeof t=="object"?Object.fromEntries(Object.entries(t).map(([a,n])=>[a,P(n,e)])):t,T=50000n,U=t=>{const e=V(t);return{domain:{name:"zkSync",version:"2",chainId:t.chainId},types:{Transaction:[{name:"txType",type:"uint256"},{name:"from",type:"uint256"},{name:"to",type:"uint256"},{name:"gasLimit",type:"uint256"},{name:"gasPerPubdataByteLimit",type:"uint256"},{name:"maxFeePerGas",type:"uint256"},{name:"maxPriorityFeePerGas",type:"uint256"},{name:"paymaster",type:"uint256"},{name:"nonce",type:"uint256"},{name:"value",type:"uint256"},{name:"data",type:"bytes"},{name:"factoryDeps",type:"bytes32[]"},{name:"paymasterInput",type:"bytes"}]},primaryType:"Transaction",message:e}};function V(t){const{gas:e,nonce:a,to:n,from:r,value:i,maxFeePerGas:s,maxPriorityFeePerGas:o,paymaster:m,paymasterInput:d,gasPerPubdata:u,data:p,factoryDeps:g}=t;return{txType:113n,from:BigInt(r),to:n?BigInt(n):0n,gasLimit:e??0n,gasPerPubdataByteLimit:u??T,maxFeePerGas:s??0n,maxPriorityFeePerGas:o??0n,paymaster:m?BigInt(m):0n,nonce:a?BigInt(a):0n,value:i??0n,data:p||"0x0",factoryDeps:(g==null?void 0:g.map(f=>c(R(f))))??[],paymasterInput:d||"0x"}}async function C(t){const{account:e,transaction:a}=t,n=await I(t),r=await w({account:e,eip712Transaction:n,chainId:a.chain.id}),i=B(a);return{transactionHash:await _(i,r),chain:a.chain,client:a.client}}async function w(t){const{account:e,eip712Transaction:a,chainId:n}=t,r=U(a),i=await e.signTypedData({...r});return Z({...a,chainId:n,customSignature:i})}async function I(t){const{account:e,transaction:a}=t,{gas:n,maxFeePerGas:r,maxPriorityFeePerGas:i,gasPerPubdata:s}=await E({transaction:a,from:z(e.address)});return{...await D({transaction:{...a,gas:n,maxFeePerGas:r,maxPriorityFeePerGas:i},from:e.address}),...a.eip712,gasPerPubdata:s,from:e.address}}function Z(t){const{chainId:e,gas:a,nonce:n,to:r,from:i,value:s,maxFeePerGas:o,maxPriorityFeePerGas:m,customSignature:d,factoryDeps:u,paymaster:p,paymasterInput:g,gasPerPubdata:f,data:F}=t,G=[n?c(n):"0x",m?c(m):"0x",o?c(o):"0x",a?c(a):"0x",r??"0x",s?c(s):"0x",F??"0x0",c(e),c(""),c(""),c(e),i??"0x",f?c(f):c(T),u??[],d??"0x",p&&g?[p,g]:[]];return A(["0x71",H(G)])}async function E(t){const{transaction:e,from:a}=t;let[n,r,i,s]=await Promise.all([y(e.gas),y(e.maxFeePerGas),y(e.maxPriorityFeePerGas),y(e.eip712)]),o=s==null?void 0:s.gasPerPubdata;if(n===void 0||r===void 0||i===void 0){const m=B(e),d=await q({transaction:e,from:a}),u=await m({method:"zks_estimateFee",params:[P(d,c)]});n=h(u.gas_limit)*2n,r=h(u.max_fee_per_gas)*2n,i=h(u.max_priority_fee_per_gas)||1n,o=h(u.gas_per_pubdata_limit)*2n,o<50000n&&(o=50000n)}return{gas:n,maxFeePerGas:r,maxPriorityFeePerGas:i,gasPerPubdata:o}}async function q(t){var m;const{transaction:e,from:a}=t,[n,r,i,s]=await Promise.all([S(e),y(e.to),y(e.value),y(e.eip712)]),o=s==null?void 0:s.gasPerPubdata;return{from:a,to:r,data:n,value:i,gasPerPubdata:o,eip712Meta:{...s,gasPerPubdata:o||50000n,factoryDeps:(m=s==null?void 0:s.factoryDeps)==null?void 0:m.map(d=>Array.from(M(d)))},type:"0x71"}}const Y=Object.freeze(Object.defineProperty({__proto__:null,getZkGasFees:E,populateEip712Transaction:I,sendEip712Transaction:C,signEip712Transaction:w},Symbol.toStringTag,{value:"Module"}));export{Y as a,I as p,w as s,h as t};
