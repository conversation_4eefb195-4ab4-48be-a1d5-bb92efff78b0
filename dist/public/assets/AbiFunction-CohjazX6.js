import{aD as d,B as l,aE as T,$ as v,aF as j,aG as x,aH as h,aI as w,a0 as y,aJ as B,r as I,aK as k}from"./index-S7-aQ7dt.js";function m(e){let t=!0,r="",n=0,s="",c=!1;for(let u=0;u<e.length;u++){const o=e[u];if(["(",")",","].includes(o)&&(t=!0),o==="("&&n++,o===")"&&n--,!!t){if(n===0){if(o===" "&&["event","function","error",""].includes(s))s="";else if(s+=o,o===")"){c=!0;break}continue}if(o===" "){e[u-1]!==","&&r!==","&&r!==",("&&(r="",t=!1);continue}s+=o,r+=o}}if(!c)throw new l("Unable to normalize signature.");return s}function g(e,t){const r=typeof e,n=t.type;switch(n){case"address":return d(e,{strict:!1});case"bool":return r==="boolean";case"function":return r==="string";case"string":return r==="string";default:return n==="tuple"&&"components"in t?Object.values(t.components).every((s,c)=>g(Object.values(e)[c],s)):/^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/.test(n)?r==="number"||r==="bigint":/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/.test(n)?r==="string"||e instanceof Uint8Array:/[a-z]+[1-9]{0,3}(\[[0-9]{0,}\])+$/.test(n)?Array.isArray(e)&&e.every(s=>g(s,{...t,type:n.replace(/(\[[0-9]{0,}\])$/,"")})):!1}}function S(e,t,r){for(const n in e){const s=e[n],c=t[n];if(s.type==="tuple"&&c.type==="tuple"&&"components"in s&&"components"in c)return S(s.components,c.components,r[n]);const u=[s.type,c.type];if(u.includes("address")&&u.includes("bytes20")?!0:u.includes("address")&&u.includes("string")?d(r[n],{strict:!1}):u.includes("address")&&u.includes("bytes")?d(r[n],{strict:!1}):!1)return u}}function D(e,t={}){const{prepare:r=!0}=t,n=Array.isArray(e)?w(e):typeof e=="string"?w(e):e;return{...n,...r?{hash:f(n)}:{}}}function N(e,t,r){const{args:n=[],prepare:s=!0}=r??{},c=T(t,{strict:!1}),u=e.filter(i=>c?i.type==="function"||i.type==="error"?E(i)===v(t,0,4):i.type==="event"?f(i)===t:!1:"name"in i&&i.name===t);if(u.length===0)throw new b({name:t});if(u.length===1)return{...u[0],...s?{hash:f(u[0])}:{}};let o;for(const i of u){if(!("inputs"in i))continue;if(!n||n.length===0){if(!i.inputs||i.inputs.length===0)return{...i,...s?{hash:f(i)}:{}};continue}if(!i.inputs||i.inputs.length===0||i.inputs.length!==n.length)continue;if(n.every((a,O)=>{const $="inputs"in i&&i.inputs[O];return $?g(a,$):!1})){if(o&&"inputs"in o&&o.inputs){const a=S(i.inputs,o.inputs,n);if(a)throw new H({abiItem:i,type:a[0]},{abiItem:o,type:a[1]})}o=i}}const p=(()=>{if(o)return o;const[i,...A]=u;return{...i,overloads:A}})();if(!p)throw new b({name:t});return{...p,...s?{hash:f(p)}:{}}}function E(e){return v(f(e),0,4)}function P(e){const t=typeof e=="string"?e:h(e);return m(t)}function f(e){return typeof e!="string"&&"hash"in e&&e.hash?e.hash:j(x(P(e)))}class H extends l{constructor(t,r){super("Found ambiguous types in overloaded ABI Items.",{metaMessages:[`\`${t.type}\` in \`${m(h(t.abiItem))}\`, and`,`\`${r.type}\` in \`${m(h(r.abiItem))}\``,"","These types encode differently and cannot be distinguished at runtime.","Remove one of the ambiguous items in the ABI."]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.AmbiguityError"})}}class b extends l{constructor({name:t,data:r,type:n="item"}){const s=t?` with name "${t}"`:r?` with data "${r}"`:"";super(`ABI ${n}${s} not found.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.NotFoundError"})}}class R extends l{constructor({data:t}){super(`Selector size is invalid. Expected 4 bytes. Received ${y(t)} bytes ("${t}").`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.InvalidSelectorSizeError"})}}function J(e,t){const{overloads:r}=e;if(y(t)<4)throw new R({data:t});if(e.inputs.length===0)return;const n=r?z([e,...r],t):e;if(!(y(t)<=4))return k(n.inputs,v(t,4))}function K(e,...t){const{overloads:r}=e,n=r?z([e,...r],e.name,{args:t[0]}):e,s=U(n),c=t.length>0?B(n.inputs,t[0]):void 0;return c?I(s,c):s}function M(e,t={}){return D(e,t)}function z(e,t,r){const n=N(e,t,r);if(n.type!=="function")throw new b({name:t,type:"function"});return n}function U(e){return E(e)}export{b as N,M as a,J as d,K as e,z as f};
