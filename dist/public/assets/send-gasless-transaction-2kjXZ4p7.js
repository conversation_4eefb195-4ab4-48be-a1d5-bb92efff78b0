const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/biconomy-BkEopQ2m.js","assets/index-62UCgHi7.js","assets/index-CFzW92q1.css","assets/openzeppelin-D5ivper6.js","assets/engine-a44_x5gm.js"])))=>i.map(i=>d[i]);
import{_ as p,R as d}from"./index-62UCgHi7.js";async function s({account:e,transaction:i,serializableTransaction:a,gasless:n}){if(a.value&&a.value>0n)throw new Error("Gasless transactions cannot have a value");let r;if(n.provider==="biconomy"){const{relayBiconomyTransaction:o}=await p(async()=>{const{relayBiconomyTransaction:t}=await import("./biconomy-BkEopQ2m.js");return{relayBiconomyTransaction:t}},__vite__mapDeps([0,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="openzeppelin"){const{relayOpenZeppelinTransaction:o}=await p(async()=>{const{relayOpenZeppelinTransaction:t}=await import("./openzeppelin-D5ivper6.js");return{relayOpenZeppelinTransaction:t}},__vite__mapDeps([3,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="engine"){const{relayEngineTransaction:o}=await p(async()=>{const{relayEngineTransaction:t}=await import("./engine-a44_x5gm.js");return{relayEngineTransaction:t}},__vite__mapDeps([4,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(!r)throw new Error("Unsupported gasless provider");return d({address:e.address,transactionHash:r.transactionHash,chainId:i.chain.id}),r}export{s as sendGaslessTransaction};
