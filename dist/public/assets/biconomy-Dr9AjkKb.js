import{s as c,a3 as y,a4 as u,w as f,a5 as m,a6 as g,j as i,a7 as l}from"./index-S7-aQ7dt.js";const d=0n;async function x({account:a,serializableTransaction:e,transaction:s,gasless:n}){const o=u({address:n.relayer<PERSON><PERSON><PERSON>erAddress,chain:s.chain,client:s.client}),r=await f({contract:o,method:"function getNonce(address,uint256) view returns (uint256)",params:[a.address,d]}),h=Math.floor(Date.now()/1e3)+(n.deadlineSeconds??3600),t={from:a.address,to:e.to,token:m,txGas:e.gas,tokenGasPrice:0n,batchId:d,batchNonce:r,deadline:h,data:e.data};if(!t.to)throw new Error("Cannot send a transaction without a `to` address");if(!t.txGas)throw new Error("Cannot send a transaction without a `gas` value");if(!t.data)throw new Error("Cannot send a transaction without a `data` value");const w=g([{type:"address"},{type:"address"},{type:"address"},{type:"uint256"},{type:"uint256"},{type:"uint256"},{type:"uint256"},{type:"bytes32"}],[i(t.from),i(t.to),i(t.token),t.txGas,t.tokenGasPrice,t.batchId,t.batchNonce,l(t.data)]),p=await a.signMessage({message:w});return[t,p]}async function C(a){const[e,s]=await x(a),n=await fetch("https://api.biconomy.io/api/v2/meta-tx/native",{method:"POST",body:c({apiId:a.gasless.apiId,params:[e,s],from:e.from,to:e.to,gasLimit:e.txGas}),headers:{"x-api-key":a.gasless.apiKey,"Content-Type":"application/json;charset=utf-8"}});if(!n.ok)throw new Error(`Failed to send transaction: ${await n.text()}`);const o=await n.json(),r=o.txHash;if(y(r))return{transactionHash:r,chain:a.transaction.chain,client:a.transaction.client};throw new Error(`Failed to send transaction: ${c(o)}`)}export{x as prepareBiconomyTransaction,C as relayBiconomyTransaction};
