import{y as s}from"./index-C23x8cdA.js";async function h(n){const i=await Promise.all(n.map(a=>r(a).then(t=>({chain:a,enabled:t}))));if(!i.every(a=>a.enabled))throw new Error(`Insight is not available for chains ${i.filter(a=>!a.enabled).map(a=>a.chain.id).join(", ")}`)}async function r(n){return(await s(n)).some(e=>e.service==="insight"&&e.enabled)}export{h as assertInsightEnabled,r as isInsightEnabled};
