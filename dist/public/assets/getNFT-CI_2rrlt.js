import{w as r,ap as e,aq as d}from"./index-S7-aQ7dt.js";import{f as o}from"./fetchTokenMetadata-Dm9TvN21.js";import{totalSupply as u}from"./totalSupply-B9a5U2XM.js";import"./detectExtension-El2m56CN.js";const i="0x0e89341c",I=[{type:"uint256",name:"tokenId"}],k=[{type:"string"}];async function m(t){return r({contract:t.contract,method:[i,I,k],params:[t.tokenId]})}async function F(t){const{useIndexer:c=!0}=t;if(c)try{return await f(t)}catch{return await a(t)}return await a(t)}async function f(t){const c=await e({client:t.contract.client,chain:t.contract.chain,contractAddress:t.contract.address,tokenId:t.tokenId});return c||a(t)}async function a(t){const[c,n]=await Promise.all([m({contract:t.contract,tokenId:t.tokenId}),u({contract:t.contract,id:t.tokenId}).catch(()=>0n)]);return d(await o({client:t.contract.client,tokenId:t.tokenId,tokenUri:c}).catch(()=>({id:t.tokenId,type:"ERC1155",uri:c})),{tokenId:t.tokenId,tokenUri:c,type:"ERC1155",owner:null,supply:n,tokenAddress:t.contract.address,chainId:t.contract.chain.id})}export{F as getNFT};
