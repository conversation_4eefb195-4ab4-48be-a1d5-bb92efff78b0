import{au as x,av as I,aw as h,ax as U,ay as f,az as r,aA as l,aB as u}from"./index-62UCgHi7.js";function i(t,n="hex"){const a=e(t),o=x(new Uint8Array(a.length));return a.encode(o),n==="hex"?I(o.bytes):o.bytes}function B(t,n="bytes"){return i(t,n)}function d(t,n="hex"){return i(t,n)}function e(t){return Array.isArray(t)?g(t.map(n=>e(n))):p(t)}function g(t){const n=t.reduce((s,c)=>s+c.length,0),a=m(n);return{length:n<=55?1+n:1+a+n,encode(s){n<=55?s.pushByte(192+n):(s.pushByte(247+a),a===1?s.pushUint8(n):a===2?s.pushUint16(n):a===3?s.pushUint24(n):s.pushUint32(n));for(const{encode:c}of t)c(s)}}}function p(t){const n=typeof t=="string"?h(t):t,a=m(n.length);return{length:n.length===1&&n[0]<128?1:n.length<=55?1+n.length:1+a+n.length,encode(s){n.length===1&&n[0]<128?s.pushBytes(n):n.length<=55?(s.pushByte(128+n.length),s.pushBytes(n)):(s.pushByte(183+a),a===1?s.pushUint8(n.length):a===2?s.pushUint16(n.length):a===3?s.pushUint24(n.length):s.pushUint32(n.length),s.pushBytes(n))}}}function m(t){if(t<2**8)return 1;if(t<2**16)return 2;if(t<2**24)return 3;if(t<2**32)return 4;throw new U("Length is too large.")}const b=2n**(8n-1n)-1n,L=2n**(16n-1n)-1n,w=2n**(24n-1n)-1n,z=2n**(32n-1n)-1n,A=2n**(40n-1n)-1n,E=2n**(48n-1n)-1n,T=2n**(56n-1n)-1n,R=2n**(64n-1n)-1n,C=2n**(72n-1n)-1n,H=2n**(80n-1n)-1n,j=2n**(88n-1n)-1n,k=2n**(96n-1n)-1n,q=2n**(104n-1n)-1n,D=2n**(112n-1n)-1n,F=2n**(120n-1n)-1n,G=2n**(128n-1n)-1n,J=2n**(136n-1n)-1n,O=2n**(144n-1n)-1n,S=2n**(152n-1n)-1n,$=2n**(160n-1n)-1n,v=2n**(168n-1n)-1n,K=2n**(176n-1n)-1n,M=2n**(184n-1n)-1n,N=2n**(192n-1n)-1n,P=2n**(200n-1n)-1n,Q=2n**(208n-1n)-1n,V=2n**(216n-1n)-1n,W=2n**(224n-1n)-1n,X=2n**(232n-1n)-1n,Y=2n**(240n-1n)-1n,Z=2n**(248n-1n)-1n,_=2n**(256n-1n)-1n,nn=-(2n**(8n-1n)),tn=-(2n**(16n-1n)),sn=-(2n**(24n-1n)),an=-(2n**(32n-1n)),on=-(2n**(40n-1n)),cn=-(2n**(48n-1n)),en=-(2n**(56n-1n)),mn=-(2n**(64n-1n)),xn=-(2n**(72n-1n)),In=-(2n**(80n-1n)),hn=-(2n**(88n-1n)),Un=-(2n**(96n-1n)),fn=-(2n**(104n-1n)),rn=-(2n**(112n-1n)),ln=-(2n**(120n-1n)),un=-(2n**(128n-1n)),gn=-(2n**(136n-1n)),pn=-(2n**(144n-1n)),yn=-(2n**(152n-1n)),Bn=-(2n**(160n-1n)),dn=-(2n**(168n-1n)),bn=-(2n**(176n-1n)),Ln=-(2n**(184n-1n)),wn=-(2n**(192n-1n)),zn=-(2n**(200n-1n)),An=-(2n**(208n-1n)),En=-(2n**(216n-1n)),Tn=-(2n**(224n-1n)),Rn=-(2n**(232n-1n)),Cn=-(2n**(240n-1n)),Hn=-(2n**(248n-1n)),jn=-(2n**(256n-1n)),kn=2n**8n-1n,qn=2n**16n-1n,Dn=2n**24n-1n,Fn=2n**32n-1n,Gn=2n**40n-1n,Jn=2n**48n-1n,On=2n**56n-1n,Sn=2n**64n-1n,$n=2n**72n-1n,vn=2n**80n-1n,Kn=2n**88n-1n,Mn=2n**96n-1n,Nn=2n**104n-1n,Pn=2n**112n-1n,Qn=2n**120n-1n,Vn=2n**128n-1n,Wn=2n**136n-1n,Xn=2n**144n-1n,Yn=2n**152n-1n,Zn=2n**160n-1n,_n=2n**168n-1n,nt=2n**176n-1n,tt=2n**184n-1n,st=2n**192n-1n,at=2n**200n-1n,ot=2n**208n-1n,ct=2n**216n-1n,it=2n**224n-1n,et=2n**232n-1n,mt=2n**240n-1n,xt=2n**248n-1n,It=2n**256n-1n;function ht(t,n){const a=n||"hex",o=f(r(t,{strict:!1})?l(t):t);return a==="bytes"?o:u(o)}export{Yn as $,M as A,N as B,P as C,Q as D,V as E,W as F,X as G,Y as H,Z as I,_ as J,kn as K,Dn as L,Fn as M,Gn as N,Jn as O,On as P,Sn as Q,$n as R,vn as S,Kn as T,Mn as U,Nn as V,Pn as W,Qn as X,Vn as Y,Wn as Z,Xn as _,It as a,Zn as a0,_n as a1,nt as a2,tt as a3,st as a4,at as a5,ot as a6,ct as a7,it as a8,et as a9,zn as aA,An as aB,En as aC,Tn as aD,Rn as aE,Cn as aF,Hn as aG,jn as aH,B as aI,d as aJ,mt as aa,xt as ab,nn as ac,tn as ad,sn as ae,an as af,on as ag,cn as ah,en as ai,mn as aj,xn as ak,In as al,hn as am,Un as an,fn as ao,rn as ap,ln as aq,un as ar,gn as as,pn as at,yn as au,Bn as av,dn as aw,bn as ax,Ln as ay,wn as az,b,L as c,w as d,z as e,A as f,E as g,T as h,R as i,C as j,H as k,j as l,qn as m,k as n,q as o,D as p,F as q,G as r,ht as s,i as t,J as u,O as v,S as w,$ as x,v as y,K as z};
