import{a0 as B,bu as x,B as p,bv as j,Z as H,bw as N,U as a,Y as E,r as I,bx as y,a4 as K,bd as k,w as D}from"./index-C23x8cdA.js";import{e as w,t as P,y as V,I as Y,f as M,v as Z}from"./Signature-IdAQOxg3.js";import{f as b}from"./Value-5LslLBXY.js";function z(t){if(!t||t.length===0)return[];const e=[];for(const{address:r,storageKeys:i}of t){for(let n=0;n<i.length;n++)if(B(i[n])!==32)throw new _({storageKey:i[n]});r&&x(r,{strict:!1}),e.push([r,i])}return e}class _ extends p{constructor({storageKey:e}){super(`Size for storage key "${e}" is invalid. Expected 32 bytes. Got ${B(e)} bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AccessList.InvalidStorageKeySizeError"})}}function q(t,e){const{as:r}=e,i=F(t),n=j(new Uint8Array(i.length));return i.encode(n),r==="Hex"?H(n.bytes):n.bytes}function v(t,e={}){const{as:r="Hex"}=e;return q(t,{as:r})}function F(t){return Array.isArray(t)?J(t.map(e=>F(e))):Q(t)}function J(t){const e=t.reduce((n,o)=>n+o.length,0),r=G(e);return{length:e<=55?1+e:1+r+e,encode(n){e<=55?n.pushByte(192+e):(n.pushByte(247+r),r===1?n.pushUint8(e):r===2?n.pushUint16(e):r===3?n.pushUint24(e):n.pushUint32(e));for(const{encode:o}of t)o(n)}}}function Q(t){const e=typeof t=="string"?N(t):t,r=G(e.length);return{length:e.length===1&&e[0]<128?1:e.length<=55?1+e.length:1+r+e.length,encode(n){e.length===1&&e[0]<128?n.pushBytes(e):e.length<=55?(n.pushByte(128+e.length),n.pushBytes(e)):(n.pushByte(183+r),r===1?n.pushUint8(e.length):r===2?n.pushUint16(e.length):r===3?n.pushUint24(e.length):n.pushUint32(e.length),n.pushBytes(e))}}}function G(t){if(t<2**8)return 1;if(t<2**16)return 2;if(t<2**24)return 3;if(t<2**32)return 4;throw new p("Length is too large.")}function R(t){const{address:e,chainId:r,nonce:i}=t,n=w(t);return[r?a(r):"0x",e,i?a(i):"0x",...n?P(n):[]]}function W(t){if(!t||t.length===0)return[];const e=[];for(const r of t)e.push(R(r));return e}class X extends p{constructor({feeCap:e}={}){super(`The fee cap (\`maxFeePerGas\`/\`maxPriorityFeePerGas\`${e?` = ${b(e)} gwei`:""}) cannot be higher than the maximum allowed value (2^256-1).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"TransactionEnvelope.FeeCapTooHighError"})}}class C extends p{constructor({gasPrice:e}={}){super(`The gas price (\`gasPrice\`${e?` = ${b(e)} gwei`:""}) cannot be higher than the maximum allowed value (2^256-1).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"TransactionEnvelope.GasPriceTooHighError"})}}class T extends p{constructor({chainId:e}){super(typeof e<"u"?`Chain ID "${e}" is invalid.`:"Chain ID is invalid."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"TransactionEnvelope.InvalidChainIdError"})}}class ee extends p{constructor({maxPriorityFeePerGas:e,maxFeePerGas:r}={}){super([`The provided tip (\`maxPriorityFeePerGas\`${e?` = ${b(e)} gwei`:""}) cannot be higher than the fee cap (\`maxFeePerGas\`${r?` = ${b(r)} gwei`:""}).`].join(`
`)),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"TransactionEnvelope.TipAboveFeeCapError"})}}function U(t){const{chainId:e,gasPrice:r,to:i}=t;if(i&&x(i,{strict:!1}),typeof e<"u"&&e<=0)throw new T({chainId:e});if(r&&BigInt(r)>2n**256n-1n)throw new C({gasPrice:r})}function te(t,e={}){const{chainId:r=0,gas:i,data:n,input:o,nonce:u,to:d,value:f,gasPrice:l}=t;U(t);let c=[u?a(u):"0x",l?a(l):"0x",i?a(i):"0x",d??"0x",f?a(f):"0x",n??o??"0x"];const s=(()=>{if(e.signature)return{r:e.signature.r,s:e.signature.s,v:V(e.signature.yParity)};if(!(typeof t.r>"u"||typeof t.s>"u"))return{r:t.r,s:t.s,v:t.v}})();if(s){const g=(()=>{if(s.v>=35)return Math.floor((s.v-35)/2)>0?s.v:27+(s.v===35?0:1);if(r>0)return r*2+35+s.v-27;const h=27+(s.v===27?0:1);if(s.v!==h)throw new Y({value:s.v});return h})();c=[...c,a(g),s.r===0n?"0x":E(a(s.r)),s.s===0n?"0x":E(a(s.s))]}else r>0&&(c=[...c,a(r),"0x","0x"]);return v(c)}const ne="0x02";function L(t){const{chainId:e,maxPriorityFeePerGas:r,maxFeePerGas:i,to:n}=t;if(e<=0)throw new T({chainId:e});if(n&&x(n,{strict:!1}),i&&BigInt(i)>2n**256n-1n)throw new X({feeCap:i});if(r&&i&&r>i)throw new ee({maxFeePerGas:i,maxPriorityFeePerGas:r})}function re(t,e={}){const{chainId:r,gas:i,nonce:n,to:o,value:u,maxFeePerGas:d,maxPriorityFeePerGas:f,accessList:l,data:c,input:s}=t;L(t);const g=z(l),h=w(e.signature||t),m=[a(r),n?a(n):"0x",f?a(f):"0x",d?a(d):"0x",i?a(i):"0x",o??"0x",u?a(u):"0x",c??s??"0x",g,...h?P(h):[]];return I(ne,v(m))}function O(t){const{chainId:e,gasPrice:r,to:i}=t;if(e<=0)throw new T({chainId:e});if(i&&x(i,{strict:!1}),r&&BigInt(r)>2n**256n-1n)throw new C({gasPrice:r})}function ie(t,e={}){const{chainId:r,gas:i,data:n,input:o,nonce:u,to:d,value:f,accessList:l,gasPrice:c}=t;O(t);const s=z(l),g=w(e.signature||t),h=[a(r),u?a(u):"0x",c?a(c):"0x",i?a(i):"0x",d??"0x",f?a(f):"0x",n??o??"0x",s,...g?P(g):[]];return I("0x01",v(h))}const ae="0x04";function A(t){const{authorizationList:e}=t;if(e)for(const r of e){const{address:i,chainId:n}=r;if(i&&x(i,{strict:!1}),Number(n)<0)throw new T({chainId:n})}L(t)}function se(t,e={}){const{authorizationList:r,chainId:i,gas:n,nonce:o,to:u,value:d,maxFeePerGas:f,maxPriorityFeePerGas:l,accessList:c,data:s,input:g}=t;A(t);const h=z(c),m=W(r),$=w(e.signature||t),S=[a(i),o?a(o):"0x",l?a(l):"0x",f?a(f):"0x",n?a(n):"0x",u??"0x",d?a(d):"0x",s??g??"0x",h,m,...$?P($):[]];return I(ae,v(S))}function oe(t){const{transaction:e}=t,r=ue(e),i=(()=>{if(t.signature)return"v"in t.signature&&typeof t.signature.v<"u"?M({r:y(t.signature.r),s:y(t.signature.s),v:Number(t.signature.v)}):{r:y(t.signature.r),s:y(t.signature.s),yParity:t.signature.yParity};if(!(typeof e.v>"u"&&typeof e.yParity>"u")){if(e.r===void 0||e.s===void 0)throw new Error("Invalid signature provided with transaction");return{r:typeof e.r=="bigint"?e.r:y(e.r),s:typeof e.s=="bigint"?e.s:y(e.s),yParity:typeof e.v<"u"&&typeof e.yParity>"u"?Z(Number(e.v)):Number(e.yParity)}}})();if(r==="eip1559"){const n=e;return L(n),re(n,{signature:i})}if(r==="legacy"){const n=e;return U(n),te(n,{signature:i})}if(r==="eip2930"){const n=e;return O(n),ie(n,{signature:i})}if(r==="eip7702"){const n=e;return A(n),se(n,{signature:i})}throw new Error("Invalid transaction type")}function ue(t){if(typeof t.type<"u")return t.type;if(typeof t.authorizationList<"u")return"eip7702";if(typeof t.maxFeePerGas<"u"||typeof t.maxPriorityFeePerGas<"u")return"eip1559";if(typeof t.gasPrice<"u")return typeof t.accessList<"u"?"eip2930":"legacy";throw new Error("Invalid transaction type")}const ce="******************************************";async function he(t){const{transaction:e,gasPriceOracleAddress:r}=t,i=K({client:e.client,address:r||ce,chain:e.chain}),{gasPrice:n,...o}=await k({transaction:e}),u=oe({transaction:o});return D({contract:i,method:"function getL1Fee(bytes memory _data) view returns (uint256)",params:[u]})}export{he as estimateL1Fee};
