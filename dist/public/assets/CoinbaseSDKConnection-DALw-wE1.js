import{P as t,L as S,Q as g}from"./index-62UCgHi7.js";function C(e){const{onBack:l,done:o,wallet:c,walletInfo:s,onGetStarted:u,locale:n}=e,[f,a]=t.useState(!1),r=t.useCallback(()=>{a(!1),c.connect({client:e.client,chain:e.chain}).then(()=>{o()}).catch(d=>{console.error(d),a(!0)})},[e.client,c,e.chain,o]),i=t.useRef(!1);return t.useEffect(()=>{i.current||(i.current=!0,r())},[r]),S.jsx(g,{locale:{getStartedLink:n.getStartedLink,instruction:n.connectionScreen.instruction,tryAgain:n.connectionScreen.retry,inProgress:n.connectionScreen.inProgress,failed:n.connectionScreen.failed},onBack:l,walletName:s.name,walletId:c.id,errorConnecting:f,onRetry:r,onGetStarted:u,client:e.client,size:e.size})}export{C as default};
