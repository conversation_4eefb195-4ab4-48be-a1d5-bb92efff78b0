const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-C23x8cdA.js","assets/index-QOfLLNNE.css"])))=>i.map(i=>d[i]);
import{ao as c,_ as s,e as d}from"./index-C23x8cdA.js";function u(a){return!!a.startsWith("data:application/json;base64")}function l(a){const[,r]=a.split(",");return c(r)}async function h(a){const{client:r,tokenId:o,tokenUri:e}=a;if(u(e))try{return JSON.parse(l(e))}catch(t){throw console.error("Failed to fetch base64 encoded NFT",{tokenId:o,tokenUri:e},t),t}const{download:n}=await s(async()=>{const{download:t}=await import("./index-C23x8cdA.js").then(i=>i.f_);return{download:t}},__vite__mapDeps([0,1]));try{if(!e.includes("{id}"))return await(await n({client:r,uri:e})).json()}catch(t){throw console.error("Failed to fetch non-dynamic NFT",{tokenId:o,tokenUri:e},t),t}try{try{return await(await n({client:r,uri:e.replace("{id}",d(o,{size:32}).slice(2))})).json()}catch{return await(await n({client:r,uri:e.replace("{id}",o.toString())})).json()}}catch(t){throw console.error("Failed to fetch dynamic NFT",{tokenId:o,tokenUri:e},t),t}}export{h as f};
